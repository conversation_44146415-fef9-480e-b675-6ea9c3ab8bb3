#!/usr/bin/env python3
"""
提取对话内容的脚本
- 对于"role": "user"的消息，提取"content"字段
- 对于"role": "assistant"的消息，优先提取"raw_content"字段，如果没有则使用"content"字段
"""

import json
import re
from pathlib import Path
from typing import List, Dict, Any


def render_html(text: str) -> str:
    """
    处理HTML标签，移除thinking部分，保留实际回复内容
    """
    # 1. 移除 <summary> 标签及其内容
    text = re.sub(r'<summary.*?>.*?</summary>', '', text, flags=re.DOTALL)

    # 2. 替换 <br> 标签为换行符
    text = text.replace('<br>', '\n').replace('<br/>', '\n')

    # 3. 移除其他 HTML 标签（保留文本内容）
    text = re.sub(r'<[^>]+>', '', text)

    # 4. 清理多余空白
    text = re.sub(r'\n\s*\n', '\n', text)  # 移除多余空行
    text = re.sub(r'[ \t]+', ' ', text)    # 合并多余空格

    return text.strip()


def remove_parentheses_and_content(text: str) -> str:
    """
    移除括号及括号内的内容（中英文括号）
    """
    # 正则表达式匹配中英文括号及括号内的内容
    pattern = r'[\(（].*?[\)）]'
    # 使用 re.sub 替换匹配的内容为空字符串
    result = re.sub(pattern, '', text)
    if result.strip():
        return result.strip()
    else:
        return text  # 如果移除后为空，返回原文本


def extract_assistant_content(msg: Dict[str, Any]) -> str:
    """
    从assistant消息中提取内容
    优先使用raw_content中</think>标签后的内容，如果没有则使用content
    """
    if 'raw_content' in msg and msg['raw_content']:
        raw_content = msg['raw_content']

        # 查找thinking部分的结束标签 </think>
        think_end_tag = "</think>"
        think_end_pos = raw_content.find(think_end_tag)

        if think_end_pos != -1:
            # 提取</think>标签之后的内容作为实际回复
            actual_content = raw_content[think_end_pos + len(think_end_tag):].strip()
            if actual_content:
                return render_html(actual_content)

        # 如果没有找到</think>标签，尝试查找</details>标签
        details_end_tag = "</details>"
        details_end_pos = raw_content.find(details_end_tag)

        if details_end_pos != -1:
            # 提取</details>标签之后的内容
            actual_content = raw_content[details_end_pos + len(details_end_tag):].strip()
            if actual_content:
                return render_html(actual_content)

        # 如果都没有找到，直接处理整个raw_content
        return render_html(raw_content)

    # 如果没有raw_content，使用content
    return msg.get('content', '')


def process_dialogue(raw_dialogue: List[Dict[str, Any]],
                    remove_brackets_in_user: bool = True) -> List[Dict[str, str]]:
    """
    处理单个对话，提取用户和助手的内容
    """
    if not raw_dialogue:
        return []

    processed_dialogue = []

    for msg in raw_dialogue:
        role = msg.get('role', '')

        if role == 'user':
            # 提取用户内容
            content = msg.get('content', '')
            if remove_brackets_in_user:
                content = remove_parentheses_and_content(content)

            processed_dialogue.append({
                'role': 'user',
                'content': content
            })

        elif role == 'assistant':
            # 提取助手内容
            content = extract_assistant_content(msg)

            processed_dialogue.append({
                'role': 'assistant',
                'content': content
            })

    return processed_dialogue


def process_dialogues_file(input_file: str,
                          output_file: str,
                          remove_brackets_in_user: bool = True) -> None:
    """
    处理整个对话文件
    """
    print(f"开始处理文件: {input_file}")

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)

        print(f"文件加载成功，开始处理对话...")

        processed_data = []
        total_dialogues = 0

        # 处理每个对话组
        for dialogue_group in raw_data:
            if isinstance(dialogue_group, list):
                processed_dialogue = process_dialogue(dialogue_group, remove_brackets_in_user)
                if processed_dialogue:  # 只添加非空对话
                    processed_data.append(processed_dialogue)
                    total_dialogues += 1

        # 保存处理后的数据
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, ensure_ascii=False, indent=2)

        print(f"处理完成！")
        print(f"- 总对话数: {total_dialogues}")
        print(f"- 输出文件: {output_file}")

    except Exception as e:
        print(f"处理文件时出错: {e}")


def main():
    """
    主函数
    """
    input_file = "merged_batch_000-500.json"
    output_file = "processed_dialogues.json"

    print("对话内容提取工具")
    print("=" * 50)

    process_dialogues_file(
        input_file=input_file,
        output_file=output_file,
        remove_brackets_in_user=True
    )


if __name__ == "__main__":
    main()